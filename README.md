# UITARS_MobileAgent
无需付费的控制手机并完成你指令的Agent！让Agent帮你操控手机点外卖，打网约车，给微信或者QQ等好友发消息.......解放你的双手！并且无需调用GPT-4o等付费api！

## 📢计划
* 为UITARS搭建memory库，实现越用越快的Agent！

## 📢新闻
* 🔥[2025.6.5] 发布了基于字节的最新SOTA模型(UITARS)的Mobile Agent。

## 📺演示
**美团点汉堡套餐**
<video src="https://private-user-images.githubusercontent.com/151722450/451905009-7f8e3e4f-e532-400b-8fb5-cb9f77db7a5d.mp4?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDkxMzI0NjAsIm5iZiI6MTc0OTEzMjE2MCwicGF0aCI6Ii8xNTE3MjI0NTAvNDUxOTA1MDA5LTdmOGUzZTRmLWU1MzItNDAwYi04ZmI1LWNiOWY3N2RiN2E1ZC5tcDQ_********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mK1x1fqOJ_6Kgo3IuDr4rkxZLqXphRi1s7SH_kKRmFI" data-canonical-src="https://private-user-images.githubusercontent.com/151722450/451905009-7f8e3e4f-e532-400b-8fb5-cb9f77db7a5d.mp4?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDkxMzI0NjAsIm5iZiI6MTc0OTEzMjE2MCwicGF0aCI6Ii8xNTE3MjI0NTAvNDUxOTA1MDA5LTdmOGUzZTRmLWU1MzItNDAwYi04ZmI1LWNiOWY3N2RiN2E1ZC5tcDQ_********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mK1x1fqOJ_6Kgo3IuDr4rkxZLqXphRi1s7SH_kKRmFI" controls="controls" muted="muted" class="d-block rounded-bottom-2 border-top width-fit" style="max-height:640px; min-height: 200px">

  </video>

**高德地图打网约车**
<video src="https://private-user-images.githubusercontent.com/151722450/451910615-82870f79-3cf9-4fe7-9b78-a91e7e86f59c.mp4?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDkxMzIyNDcsIm5iZiI6MTc0OTEzMTk0NywicGF0aCI6Ii8xNTE3MjI0NTAvNDUxOTEwNjE1LTgyODcwZjc5LTNjZjktNGZlNy05Yjc4LWE5MWU3ZTg2ZjU5Yy5tcDQ_********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RluoXd4TiyPpRU8hKzMoM1JeOUJbu0LMxnZo3V5FatQ" data-canonical-src="https://private-user-images.githubusercontent.com/151722450/451910615-82870f79-3cf9-4fe7-9b78-a91e7e86f59c.mp4?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDkxMzIyNDcsIm5iZiI6MTc0OTEzMTk0NywicGF0aCI6Ii8xNTE3MjI0NTAvNDUxOTEwNjE1LTgyODcwZjc5LTNjZjktNGZlNy05Yjc4LWE5MWU3ZTg2ZjU5Yy5tcDQ_********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RluoXd4TiyPpRU8hKzMoM1JeOUJbu0LMxnZo3V5FatQ" controls="controls" muted="muted" class="d-block rounded-bottom-2 border-top width-fit" style="max-height:640px; min-height: 200px">

  </video>

**抖音搜索视频分享给好友**
<video src="https://private-user-images.githubusercontent.com/151722450/451904185-a4ecd63e-f412-4121-bf4e-9bed8f291e8f.mp4?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDkxMzI1MzMsIm5iZiI6MTc0OTEzMjIzMywicGF0aCI6Ii8xNTE3MjI0NTAvNDUxOTA0MTg1LWE0ZWNkNjNlLWY0MTItNDEyMS1iZjRlLTliZWQ4ZjI5MWU4Zi5tcDQ_********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P6_Xlm0qeCTeTCipVH2letIvfMH3HBlfkrlLnJcQifk" data-canonical-src="https://private-user-images.githubusercontent.com/151722450/451904185-a4ecd63e-f412-4121-bf4e-9bed8f291e8f.mp4?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDkxMzI1MzMsIm5iZiI6MTc0OTEzMjIzMywicGF0aCI6Ii8xNTE3MjI0NTAvNDUxOTA0MTg1LWE0ZWNkNjNlLWY0MTItNDEyMS1iZjRlLTliZWQ4ZjI5MWU4Zi5tcDQ_********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P6_Xlm0qeCTeTCipVH2letIvfMH3HBlfkrlLnJcQifk" controls="controls" muted="muted" class="d-block rounded-bottom-2 border-top width-fit" style="max-height:640px; min-height: 200px">

  </video>


## 📋介绍

* 本项目基于字节的UITARS模型和阿里的MobileAgent框架。
* 本项目框架构建思路部分参考OSWorld中uitars的工作。
* 本项目基于单智能体轻松实现点外卖，打网约车，给微信或者QQ等好友发消息等等功能

## 🔧开始

❗目前仅安卓和鸿蒙系统（版本号 <= 4）支持工具调试。其他系统如iOS暂时不支持。

### 安装依赖
创建虚拟环境，并且安装依赖
```
pip install -r requirements.txt
```

### 详细运行教程

教程即将来临

### run_uitars.py中改动

1.即将来临

### 运行
```
python run_uitars.py
```
