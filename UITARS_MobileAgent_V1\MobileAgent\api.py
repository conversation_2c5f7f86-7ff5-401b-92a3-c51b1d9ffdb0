import base64
import requests
from PIL import Image
import io

def encode_image(image_path):
    # 确保图像是JPEG格式
    image = Image.open(image_path)
    if image.mode != 'RGB':
        image = image.convert('RGB')

    # 将图像转换为JPEG格式的字节流
    buffer = io.BytesIO()
    image.save(buffer, format='JPEG', quality=95)
    buffer.seek(0)

    # 编码为base64
    return base64.b64encode(buffer.getvalue()).decode('utf-8')


def inference_chat_uitars(chat, model, api_url, token):
    headers = {
        "Content-Type": "application/json",
        'Accept': 'application/json',
        "Authorization": f"Bearer {token}"
    }

    data = {
        "model": model,
        "messages": [],
        "max_tokens": 2048,
        'temperature': 0.0,
        "seed": 1234
    }

    # for role, content in chat:
    #     data["messages"].append({"role": role, "content": content})

    for message in chat:
        data["messages"].append({
            "role": message["role"],
            "content": message["content"]
        })

    while True:
        try:
            res = requests.post(api_url, headers=headers, json=data)
            res_json = res.json()
            res_content = res_json['choices'][0]['message']['content']
        except:
            print("Network Error:")
            try:
                print(res.json())
            except:
                print("Request Failed")
        else:
            break
    
    return res_content


def inference_chat(chat, model, api_url, token):
    headers = {
        "Content-Type": "application/json",
        'Accept': 'application/json',
        "Authorization": f"Bearer {token}"
    }

    data = {
        "model": model,
        "messages": [],
        "max_tokens": 2048,
        'temperature': 0.0,
        "seed": 1234
    }

    for role, content in chat:
        data["messages"].append({"role": role, "content": content})

    # for message in chat:
    #     data["messages"].append({
    #         "role": message["role"],
    #         "content": message["content"]
    #     })

    while True:
        try:
            res = requests.post(api_url, headers=headers, json=data)
            res_json = res.json()
            res_content = res_json['choices'][0]['message']['content']
        except:
            print("Network Error:")
            try:
                print(res.json())
            except:
                print("Request Failed")
        else:
            break

    return res_content