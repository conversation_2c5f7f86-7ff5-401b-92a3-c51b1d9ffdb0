import requests
import base64
from PIL import Image
import io

def test_doubao_api():
    # 创建一个简单的测试图像
    img = Image.new('RGB', (100, 100), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    buffer.seek(0)
    base64_image = base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer 7f1342d5-b912-4100-9040-4ae8bb3dd610"
    }
    
    # 测试不同的消息格式
    data = {
        "model": "doubao-1-5-thinking-vision-pro-250428",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请描述这张图片"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ],
        "max_tokens": 1000
    }
    
    try:
        response = requests.post(
            "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
            headers=headers,
            json=data
        )
        print("Status Code:", response.status_code)
        print("Response:", response.json())
    except Exception as e:
        print("Error:", e)
        print("Response text:", response.text if 'response' in locals() else "No response")

if __name__ == "__main__":
    test_doubao_api()
